import { Box, FormControl, FormHelperText } from "@mui/material";
import React from "react";
import { CustomInputLabel } from "src/modules/Common/FormInputs/CustomInputLabel";
import GroupedInput from "src/modules/Common/FormInputs/GroupedInput";
import { useFieldContext } from "../../effiFormContext";

type EffiGroupedInputProps = {
  label: string;
  required?: boolean;
  segmentLengths: number[];
};

const EffiGroupedInput: React.FC<EffiGroupedInputProps> = ({ label, required, segmentLengths, ...props }) => {
  const field = useFieldContext();
  console.log("field", field.state?.meta?.errors);
  return (
    <Box display="flex" flexDirection="column">
      <CustomInputLabel title={label} id={field.name} required={required} data-testId={field.name} />
      <FormControl error={field.state?.meta?.errors?.length > 0}>
        <GroupedInput
          segmentLengths={segmentLengths}
          value={(field.state.value as string[]) || []}
          onChange={(value) => field.handleChange(value || [])}
          {...props}
        />
        {field.state?.meta?.errors?.length > 0 && (
          <FormHelperText error>{field.state?.meta?.errors?.[0]?.message}</FormHelperText>
        )}
      </FormControl>
    </Box>
  );
};

export default EffiGroupedInput;
