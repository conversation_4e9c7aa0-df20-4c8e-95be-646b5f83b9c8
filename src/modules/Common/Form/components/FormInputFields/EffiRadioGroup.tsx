import { Box, FormControl, FormControlLabel, Radio, RadioGroup, Typography } from "@mui/material";
import React from "react";
import { CustomInputLabel } from "src/modules/Common/FormInputs/CustomInputLabel";
import { useFieldContext } from "../../effiFormContext";

type RadioOption = {
  value: string;
  label: string;
  subLabel?: string;
  disabled?: boolean;
};

type EffiRadioGroupProps = {
  label: string;
  required?: boolean;
  options: RadioOption[];
  layout?: "horizontal" | "vertical";
  size?: "small" | "medium";
};

const EffiRadioGroup: React.FC<EffiRadioGroupProps> = ({
  label,
  required,
  options,
  layout = "vertical",
  size = "small",
}) => {
  const field = useFieldContext();

  const renderRadioOption = (option: RadioOption) => {
    const radioControl = <Radio size={size} disabled={option.disabled} />;

    const labelContent = (
      <Box display="flex" flexDirection="column">
        <Typography variant="body2" component="span">
          {option.label}
        </Typography>
        {option.subLabel && (
          <Typography variant="caption" component="span" color="text.secondary" sx={{ marginTop: "2px" }}>
            {option.subLabel}
          </Typography>
        )}
      </Box>
    );

    return (
      <FormControlLabel
        key={option.value}
        value={option.value}
        control={radioControl}
        label={labelContent}
        disabled={option.disabled}
        sx={{
          alignItems: option.subLabel ? "flex-start" : "center",
          marginBottom: layout === "vertical" ? "8px" : "0",
          marginRight: layout === "horizontal" ? "16px" : "0",
          "& .MuiFormControlLabel-label": {
            marginTop: option.subLabel ? "0px" : "0px",
            marginLeft: "4px",
          },
          "& .MuiRadio-root": {
            paddingTop: option.subLabel ? "6px" : "9px",
            paddingBottom: "9px",
          },
        }}
      />
    );
  };

  return (
    <Box display="flex" flexDirection="column">
      <CustomInputLabel title={label} id={field.name} required={required} />
      <FormControl error={field.state?.meta?.errors?.length > 0}>
        <RadioGroup
          name={field.name}
          value={field.state.value || ""}
          onChange={(event) => field.handleChange(event.target.value)}
          row={layout === "horizontal"}
          sx={{
            flexDirection: layout === "horizontal" ? "row" : "column",
            gap: layout === "vertical" ? "4px" : "8px",
          }}
        >
          {options?.map(renderRadioOption)}
        </RadioGroup>
      </FormControl>
    </Box>
  );
};

export default EffiRadioGroup;
