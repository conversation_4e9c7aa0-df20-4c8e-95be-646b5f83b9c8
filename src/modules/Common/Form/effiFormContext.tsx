import { createF<PERSON>Hook, createFormHookContexts } from "@tanstack/react-form";
import EffiCurrency from "./components/FormInputFields/AdvancedCompositions/EffiCurrency";
import EffiPercentageField from "./components/FormInputFields/AdvancedCompositions/EffiPercentageField";
import EffiAutoComplete from "./components/FormInputFields/EffiAutoComplete";
import EffiCheckbox from "./components/FormInputFields/EffiCheckbox";
import EffiDate from "./components/FormInputFields/EffiDate";
import EffiFileUpload from "./components/FormInputFields/EffiFileUpload";
import EffiGroupedInput from "./components/FormInputFields/EffiGroupedInput";
import EffiMultiSelect from "./components/FormInputFields/EffiMultiSelect";
import EffiPhone from "./components/FormInputFields/EffiPhone";
import EffiRadio from "./components/FormInputFields/EffiRadio";
import EffiRadioGroup from "./components/FormInputFields/EffiRadioGroup";
import EffiSelect from "./components/FormInputFields/EffiSelect";
import EffiSwitch from "./components/FormInputFields/EffiSwitch";
import EffiTextField from "./components/FormInputFields/EffiTextField";

export const { fieldContext, formContext, useFieldContext } = createFormHookContexts();

export const { useAppForm } = createFormHook({
  fieldContext,
  formContext,
  fieldComponents: {
    EffiTextField,
    EffiSelect,
    EffiDate,
    EffiSwitch,
    EffiPercentageField,
    EffiCurrency,
    EffiMultiSelect,
    EffiFileUpload,
    EffiPhone,
    EffiAutoComplete,
    EffiCheckbox,
    EffiRadio,
    EffiGroupedInput,
    EffiRadioGroup,
  },
  formComponents: {},
});
