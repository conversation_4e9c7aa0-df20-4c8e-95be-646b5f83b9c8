import { Box, Button, Grid2, MenuItem } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React from "react";
import { useAppForm } from "src/modules/Common/Form/effiFormContext";
import Modal from "src/modules/Common/Modal/Modal";
import payrollService from "src/services/payroll.service";
import { z } from "zod";
import { Component } from "./Compensation";

const addComponentSchema = z.object({
  component_name: z.string().nonempty({
    message: "Component is required",
  }),
  amount: z.number().min(0, {
    message: "Amount must be greater than or equal to 0",
  }),
});

type Props = {
  isOpen: boolean;
  onClose: () => void;
  onAddComponent: (componentData: Component) => void;
  currency?: string;
  country: string;
  existingComponents: Component[];
};

const AddComponentModal: React.FC<Props> = ({
  isOpen,
  onClose,
  onAddComponent,
  currency = "INR",
  country,
  existingComponents
}) => {
  const { data: allComponents = [], isLoading: isComponentsLoading } = useQuery(
    ["get-all-compensation-components", country],
    async () => {
      const components = await payrollService.getAllCompensationComponents(country);
      return components || [];
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      refetchOnReconnect: false,
      enabled: isOpen && !!country,
    },
  );

  const form = useAppForm({
    defaultValues: {
      component_name: "",
      amount: 0,
    },
    validators: {
      onSubmit: addComponentSchema,
    },
    onSubmit: ({ value }) => {
      const selectedComponent = allComponents.find(
        (component) => component.name === value.component_name,
      );

      if (selectedComponent) {
        // Create a new component with the user-specified amount
        const newComponent: Component = {
          compensation_component: {
            ...selectedComponent,
            currency: currency,
            formula: {
              ...selectedComponent.formula,
              value: value.amount,
            },
          },
          amount: value.amount,
        };
        onAddComponent(newComponent);
      }
      onClose();
    },
  });

  console.log({ existingComponents, allComponents });
  

  // Filter components to only show earnings that are not already selected
  const availableEarningsComponents = allComponents.filter(
    (component) =>
      component.component_type === "Earning" &&
      !existingComponents.some(
        (existing) => existing.compensation_component.name === component.name
      )
  );

  const handleClose = () => {
    form.reset();
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      showBackButton
      title="Add Custom Component"
      actions={
        <form.Subscribe selector={(state) => [state.canSubmit, state.isSubmitting, state.isPristine]}>
          {([canSubmit, isSubmitting, isPristine]) => (
            <Box display="flex" p={2} gap={1} justifyContent="flex-end">
              <Button variant="outlined" onClick={handleClose}>
                Cancel
              </Button>
              <Button
                variant="contained"
                type="submit"
                disabled={!canSubmit || isPristine || isSubmitting}
                onClick={form.handleSubmit}
              >
                Add Component
              </Button>
            </Box>
          )}
        </form.Subscribe>
      }
    >
      <Grid2 container spacing={2}>
        <Grid2 size={12}>
          <form.AppField name="component_name">
            {(field: any) => (
              <field.EffiSelect
                label="Select Component"
                required
                disabled={isComponentsLoading || availableEarningsComponents.length === 0}
              >
                {availableEarningsComponents.map((component) => (
                  <MenuItem
                    key={component.name}
                    value={component.name}
                  >
                    {component.name}
                  </MenuItem>
                ))}
              </field.EffiSelect>
            )}
          </form.AppField>
        </Grid2>
        <Grid2 size={12}>
          <form.AppField name="amount">
            {(field: any) => (
              <field.EffiCurrency
                label="Amount"
                currency={currency}
                required
                endHelperText="Annual"
              />
            )}
          </form.AppField>
        </Grid2>
      </Grid2>
    </Modal>
  );
};

export default AddComponentModal;
