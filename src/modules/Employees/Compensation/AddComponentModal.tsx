import { Box, Button, Grid2, MenuItem } from "@mui/material";
import { useStore } from "@tanstack/react-form";
import { useQuery } from "@tanstack/react-query";
import React from "react";
import { useAppForm } from "src/modules/Common/Form/effiFormContext";
import Modal from "src/modules/Common/Modal/Modal";
import { PayrollTemplateV2 } from "src/services/api_definitions/payroll.service";
import payrollService from "src/services/payroll.service";
import { z } from "zod";

const addComponentSchema = z.object({
  template_name: z.string().nonempty({
    message: "Template is required",
  }),
  component_name: z.string().nonempty({
    message: "Component is required",
  }),
  amount: z.number().min(0, {
    message: "Amount must be greater than or equal to 0",
  }),
});

type AddComponentFormData = z.infer<typeof addComponentSchema>;

type Props = {
  isOpen: boolean;
  onClose: () => void;
  onAddComponent: (componentData: any) => void;
  currency?: string;
};

const AddComponentModal: React.FC<Props> = ({ isOpen, onClose, onAddComponent, currency = "INR" }) => {
  const { data: templates = [], isLoading: isTemplatesLoading } = useQuery(
    ["get-all-templates"],
    async () => {
      const templates = await payrollService.getAllTemplatesV2();
      return templates || [];
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      refetchOnReconnect: false,
      enabled: isOpen,
    },
  );

  const form = useAppForm({
    defaultValues: {
      template_name: "",
      component_name: "",
      amount: 0,
    },
    validators: {
      onSubmit: addComponentSchema,
    },
    onSubmit: ({ value }) => {
      const selectedTemplate = templates.find((template) => template.name === value.template_name);
      const selectedComponent = selectedTemplate?.components?.find(
        (component) => component.compensation_component.name === value.component_name,
      );

      if (selectedComponent) {
        // Create a new component with the user-specified amount
        const newComponent = {
          ...selectedComponent,
          amount: value.amount,
          compensation_component: {
            ...selectedComponent.compensation_component,
            currency: currency,
            formula: {
              ...selectedComponent.compensation_component.formula,
              value: value.amount,
            },
          },
        };
        onAddComponent(newComponent);
      }
      onClose();
    },
  });

  const selectedTemplateName = useStore(form.store, (state) => state.values.template_name);
  const selectedTemplate = templates.find((template) => template.name === selectedTemplateName);

  // Filter components to only show earnings
  const earningsComponents = selectedTemplate?.components?.filter(
    (component) => component.compensation_component.component_type === "Earning",
  ) || [];

  const handleClose = () => {
    form.reset();
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      showBackButton
      title="Add Custom Component"
      actions={
        <form.Subscribe selector={(state) => [state.canSubmit, state.isSubmitting, state.isPristine]}>
          {([canSubmit, isSubmitting, isPristine]) => (
            <Box display="flex" p={2} gap={1} justifyContent="flex-end">
              <Button variant="outlined" onClick={handleClose}>
                Cancel
              </Button>
              <Button
                variant="contained"
                type="submit"
                disabled={!canSubmit || isPristine || isSubmitting}
                onClick={form.handleSubmit}
              >
                Add Component
              </Button>
            </Box>
          )}
        </form.Subscribe>
      }
    >
      <Grid2 container spacing={2}>
        <Grid2 size={12}>
          <form.AppField name="template_name">
            {(field: any) => (
              <field.EffiSelect label="Select Template" required disabled={isTemplatesLoading}>
                {templates.map((template) => (
                  <MenuItem key={template.name} value={template.name}>
                    {template.name}
                  </MenuItem>
                ))}
              </field.EffiSelect>
            )}
          </form.AppField>
        </Grid2>
        <Grid2 size={12}>
          <form.AppField name="component_name">
            {(field: any) => (
              <field.EffiSelect 
                label="Select Component" 
                required 
                disabled={!selectedTemplateName || earningsComponents.length === 0}
              >
                {earningsComponents.map((component) => (
                  <MenuItem 
                    key={component.compensation_component.name} 
                    value={component.compensation_component.name}
                  >
                    {component.compensation_component.name}
                  </MenuItem>
                ))}
              </field.EffiSelect>
            )}
          </form.AppField>
        </Grid2>
        <Grid2 size={12}>
          <form.AppField name="amount">
            {(field: any) => (
              <field.EffiCurrency 
                label="Amount" 
                currency={currency} 
                required 
                endHelperText="Annual"
              />
            )}
          </form.AppField>
        </Grid2>
      </Grid2>
    </Modal>
  );
};

export default AddComponentModal;
