import { AddRounded, CalculateRounded, CheckRounded, CloseRounded } from "@mui/icons-material";
import { Box, Button, Grid2, IconButton, MenuItem, Select, TextField, Typography } from "@mui/material";
import { useStore } from "@tanstack/react-form";
import { useQuery } from "@tanstack/react-query";
import React, { useEffect, useMemo, useState } from "react";
import DataTable from "src/modules/Common/Table/DataTable";
import EmployeeCompensation, {
  formatCurrency,
} from "src/modules/Profile/components/EmployeeCompensation/EmployeeCompensation";
import { PayrollComponentV2, PayrollTemplateV2 } from "src/services/api_definitions/payroll.service";
import payrollService from "src/services/payroll.service";
import { Component } from "./Compensation";

type Props = {
  form: any;
  isCurrentCompensation?: boolean;
  templates: PayrollTemplateV2[];
  isReadOnly?: boolean;
  preview: any;
  setPreview: (preview: any) => void;
};

const CompensationItem: React.FC<Props> = ({
  form,
  isCurrentCompensation,
  templates,
  isReadOnly,
  preview,
  setPreview,
}) => {
  const [isCreatingRow, setIsCreatingRow] = useState(false);
  const [newComponent, setNewComponent] = useState({
    component_name: "",
    amount: 0,
  });

  const compensationDetails = useStore(
    form.store,
    (state: any) => state.values[isCurrentCompensation ? "current_compensation" : "next_compensation"],
  );

  const currency =
    compensationDetails?.components &&
    compensationDetails?.components?.reduce((_: any, curr: Component) => {
      return curr?.compensation_component?.currency;
    }, "");

  const template = useMemo(() => {
    const template = templates?.find((template) => template.name === compensationDetails?.name);
    return template;
  }, [templates, compensationDetails]);

  // Fetch all compensation components for inline row creation
  const { data: allComponents = [] } = useQuery(
    ["get-all-compensation-components", "India"],
    async () => {
      const components = await payrollService.getAllCompensationComponents("India");
      return components || [];
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      refetchOnReconnect: false,
      enabled: !isReadOnly && !!template,
    },
  );

  // Filter available earning components that are not already selected
  const availableEarningsComponents = allComponents.filter(
    (component) =>
      component.component_type === "Earning" &&
      !compensationDetails?.components?.some(
        (existing: Component) => existing.compensation_component.name === component.name
      )
  );
  const groupedCompensationDetails = useMemo(() => {
    const compensationDetailsByCompensationType = template?.components?.reduce(
      (acc, item) => {
        if (!acc[item?.compensation_component?.component_type]) {
          acc[item?.compensation_component?.component_type] = [item];
          return acc;
        }
        acc[item?.compensation_component?.component_type].push(item);
        return acc;
      },
      {} as Record<string, any[]>,
    );
    const results: any[] = [];
    if (!compensationDetailsByCompensationType) return [];
    Object.entries(compensationDetailsByCompensationType).forEach(([compensationType, compensationDetails]) => {
      results.push({
        name: `${compensationType}s`,
        isSection: true,
        component_type: compensationType,
        monthly: "",
        annually: "",
        description: "",
      });
      compensationDetails.forEach((compensationDetail) => {
        results.push({
          ...compensationDetail,
          name: compensationDetail?.compensation_component?.name,
          isSection: false,
          monthly: compensationDetail?.amount,
          annually: compensationDetail?.amount * 12,
        });
      });

      // Add inline form row for earnings section
      if (compensationType === "Earning" && isCreatingRow && !isReadOnly && availableEarningsComponents.length > 0) {
        results.push({
          name: "ADD_NEW_COMPONENT",
          isSection: false,
          isAddingNew: true,
          component_type: "Earning",
          monthly: "",
          annually: "",
          description: "",
        });
      }
    });
    return results;
  }, [compensationDetails, isCreatingRow, isReadOnly, availableEarningsComponents]);

  useEffect(() => {
    const key = isCurrentCompensation ? "current_compensation" : "next_compensation";
    if (compensationDetails?.components?.length === 0) {
      template?.components?.forEach((component: any) => {
        form.pushFieldValue(`${key}.components`, {
          ...component,
          amount: 0,
        });
      });
    }
  }, [template]);

  const getComponentByCalculationType = (row: Component) => {
    const indexOfComponent = compensationDetails?.components?.findIndex(
      (eachComponent: any) => eachComponent.compensation_component.name === row.compensation_component.name,
    );
    const key = `${isCurrentCompensation ? "current_compensation" : "next_compensation"}.components[${indexOfComponent}]`;
    switch (row?.compensation_component?.calculation_type) {
      case "Flat":
        return (
          <form.AppField name={`${key}.compensation_component.formula.value`}>
            {(field: any) => (
              <field.EffiCurrency label="" currency={row?.compensation_component.currency} endHelperText="Annual" />
            )}
          </form.AppField>
        );
      case "Percentage":
        return (
          <form.AppField name={`${key}.compensation_component.formula.value`}>
            {(field: any) => (
              <field.EffiPercentageField
                label=""
                endHelperText={`of ${row?.compensation_component?.formula?.display_name}`}
              />
            )}
          </form.AppField>
        );
      case "Formula":
        return (
          <form.AppField name={`${key}.compensation_component.formula.value`}>
            {(field: any) => <field.EffiCurrency currency={row?.compensation_component?.currency} label="" />}
          </form.AppField>
        );
      default:
        return row?.compensation_component?.calculation_type;
    }
  };

  const onComputeClick = async () => {
    try {
      const resp = await payrollService.computePreview(
        compensationDetails?.components?.map((eachComponent: PayrollComponentV2) => ({
          ...eachComponent?.compensation_component,
        })),
        compensationDetails?.ctc,
      );
      setPreview((prev: any) => ({
        ...prev,
        [isCurrentCompensation ? "current_compensation" : "next_compensation"]: resp?.components,
      }));
      return true;
    } catch (err) {
      console.log(err);
      return false;
    }
  };

  const handleAddComponent = () => {
    const selectedComponent = availableEarningsComponents.find(
      (component) => component.name === newComponent.component_name,
    );

    if (selectedComponent && newComponent.amount > 0) {
      const componentToAdd: Component = {
        compensation_component: {
          ...selectedComponent,
          currency: currency || "INR",
          formula: {
            ...selectedComponent.formula,
            value: newComponent.amount,
          },
        },
        amount: newComponent.amount,
      };

      const key = isCurrentCompensation ? "current_compensation" : "next_compensation";
      form.pushFieldValue(`${key}.components`, componentToAdd);

      // Reset form
      setNewComponent({ component_name: "", amount: 0 });
      setIsCreatingRow(false);
    }
  };

  const handleCancelAdd = () => {
    setNewComponent({ component_name: "", amount: 0 });
    setIsCreatingRow(false);
  };

  if (isReadOnly) {
    return (
      <EmployeeCompensation
        compensation={compensationDetails?.components}
        currency={currency}
        ctc={compensationDetails?.ctc}
      />
    );
  }

  return (
    <Box display="flex" flexDirection="column" gap={1}>
      <Grid2 container spacing={2} alignItems="flex-end">
        <Grid2 size={6}>
          <form.AppField name={isCurrentCompensation ? "current_compensation.ctc" : "next_compensation.ctc"}>
            {(field: any) => (
              <field.EffiCurrency inputMode="numeric" label="CTC" currency={currency} endHelperText="Anually" />
            )}
          </form.AppField>
        </Grid2>
        <Grid2 size={3} alignItems="flex-end">
          <Box>
            <IconButton color="primary" onClick={onComputeClick}>
              <CalculateRounded fontSize="large" />
            </IconButton>
          </Box>
        </Grid2>
      </Grid2>
      <DataTable
        renderTopToolbar={() => (
          <Box p={2}>
            <Typography variant="h6">Salary Breakup</Typography>
          </Box>
        )}
        enableTopToolbar
        enableKeyboardShortcuts={false}
        enableRowActions={!isReadOnly && !!template && availableEarningsComponents.length > 0}
        renderRowActions={({ row }) => (
          <Box sx={{ display: 'flex', gap: '0.5rem' }}>
            {row.original.isSection && row.original.component_type === "Earning" && !isCreatingRow && (
              <IconButton
                onClick={() => setIsCreatingRow(true)}
                title="Add Component"
                size="small"
              >
                <AddRounded />
              </IconButton>
            )}
          </Box>
        )}
        columns={[
          {
            accessorKey: "name",
            header: "Component",
            size: 250,
            Cell: ({ row }) => {
              if (row?.original?.isSection) return row.original.name;
              if (row?.original?.isAddingNew) {
                return (
                  <Select
                    value={newComponent.component_name}
                    onChange={(e) => setNewComponent(prev => ({ ...prev, component_name: e.target.value }))}
                    size="small"
                    fullWidth
                    displayEmpty
                  >
                    <MenuItem value="">Select Component</MenuItem>
                    {availableEarningsComponents.map((component) => (
                      <MenuItem key={component.name} value={component.name}>
                        {component.name}
                      </MenuItem>
                    ))}
                  </Select>
                );
              }
              return row.original.name;
            },
          },
          {
            header: "Component Type",
            size: 250,
            Cell: ({ row }) => {
              if (row?.original?.isSection) return null;
              if (row?.original?.isAddingNew) {
                return (
                  <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                    <TextField
                      value={newComponent.amount}
                      onChange={(e) => setNewComponent(prev => ({ ...prev, amount: Number(e.target.value) }))}
                      type="number"
                      size="small"
                      placeholder="Enter amount"
                      slotProps={{ htmlInput: { min: 0 } }}
                      sx={{ flex: 1 }}
                    />
                    <IconButton
                      onClick={handleAddComponent}
                      disabled={!newComponent.component_name || newComponent.amount <= 0}
                      size="small"
                      color="primary"
                      title="Add Component"
                    >
                      <CheckRounded />
                    </IconButton>
                    <IconButton
                      onClick={handleCancelAdd}
                      size="small"
                      color="error"
                      title="Cancel"
                    >
                      <CloseRounded />
                    </IconButton>
                  </Box>
                );
              }
              return getComponentByCalculationType(row?.original);
            },
          },
          {
            header: "Monthly Amount",
            size: 100,
            Cell: ({ row }) => {
              const value = preview?.[row.original?.compensation_component?.name];

              if (row?.original?.isSection || row?.original?.isAddingNew) {
                return null;
              }

              if (!value) {
                return 0;
              }
              return formatCurrency(value / 12, row?.original?.compensation_component?.currency as string);
            },
          },
          {
            header: "Annual Amount",
            size: 100,
            Cell: ({ row }) => {
              const value = preview?.[row.original?.compensation_component?.name];

              if (row?.original?.isSection || row?.original?.isAddingNew) {
                return null;
              }

              if (!value) {
                return 0;
              }
              return formatCurrency(value, row?.original?.compensation_component?.currency as string);
            },
          },
        ]}
        data={groupedCompensationDetails || []}
        muiTableBodyRowProps={({ row }) => ({
          sx: {
            backgroundColor: row.original.isSection ? "#F8FFFE" : "inherit",
            ...(row.original.isSection && {
              borderStyle: "dashed",
            }),
          },
        })}
      />
    </Box>
  );
};

export default CompensationItem;
